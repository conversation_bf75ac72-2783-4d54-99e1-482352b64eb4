import { useState } from "react";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MessageCircle, ExternalLink } from "lucide-react";
import Navigation from "@/components/layout/navigation";
import WhatsAppVerification from "@/components/auth/whatsapp-verification";
import ProductGrid from "@/components/product/product-grid";
import SearchFilters from "@/components/product/search-filters";
import WishlistModal from "@/components/wishlist/wishlist-modal";
import ProductModal from "@/components/product/product-modal";
import ContactSection from "@/components/contact/contact-section";
import ScrollToTop from "@/components/ui/scroll-to-top";
import { useAuth } from "@/hooks/use-auth";
import type { Product } from "@shared/schema";
import vmakeLogo from "@assets/339076826_1147709369229224_1319750110613322317_n.jpg";

export default function Home() {
  const { user, isLoading } = useAuth();
  const [, setLocation] = useLocation();
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isWishlistOpen, setIsWishlistOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [filters, setFilters] = useState({
    category: "all",
    finish: "all",
    material: "all",
    sortBy: "name",
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black-primary flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold"></div>
      </div>
    );
  }

  // Handle contact actions
  const handleWhatsAppContact = () => {
    window.open('https://api.whatsapp.com/send?phone=918882636296&text=Hi, I would like to access the VMake Finessee catalog. Please provide me with access.', '_blank');
  };

  const handleGoogleContact = () => {
    window.open('https://g.co/kgs/evVbwcC', '_blank');
  };

  // Show contact options for non-admin users
  if (!user || !user.isAdmin) {
    return (
      <div className="min-h-screen bg-black-primary flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-black-secondary border-black-accent">
          <CardContent className="pt-6 text-center">
            <div className="w-20 h-20 mx-auto mb-6 bg-gold rounded-full flex items-center justify-center overflow-hidden">
              <img
                src={vmakeLogo}
                alt="VMake Finessee Logo"
                className="w-full h-full object-cover"
              />
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">VMake Finessee</h2>
            <h3 className="text-lg font-semibold text-gold mb-4">Catalog Access Required</h3>
            <p className="text-gray-300 mb-6">
              To view our premium catalog, please contact our admin for access.
            </p>

            <div className="space-y-3">
              <Button
                onClick={handleWhatsAppContact}
                className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Contact Admin on WhatsApp
              </Button>

              <Button
                onClick={handleGoogleContact}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Visit Our Google Page
              </Button>
            </div>

            <p className="text-xs text-gray-500 mt-6">
              Click the buttons above to get catalog access from our admin
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black-primary text-white">
      <Navigation
        onWishlistClick={() => setIsWishlistOpen(true)}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <SearchFilters
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          filters={filters}
          onFiltersChange={setFilters}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
        />

        <ProductGrid
          searchQuery={searchQuery}
          filters={filters}
          onProductSelect={setSelectedProduct}
          viewMode={viewMode}
        />

        {/* Contact Section */}
        <ContactSection />
      </div>

      <WishlistModal
        isOpen={isWishlistOpen}
        onClose={() => setIsWishlistOpen(false)}
      />

      {selectedProduct && (
        <ProductModal
          product={selectedProduct}
          isOpen={!!selectedProduct}
          onClose={() => setSelectedProduct(null)}
        />
      )}

      {/* Scroll to Top Button */}
      <ScrollToTop />
    </div>
  );
}
